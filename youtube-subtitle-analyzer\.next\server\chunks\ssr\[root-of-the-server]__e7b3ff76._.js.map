{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface AnalysisResult {\n  success: boolean;\n  videoId: string;\n  transcriptLength: number;\n  analysis: string;\n  rawTranscript: Array<{ text: string; offset: number; duration: number }>;\n}\n\nexport default function Home() {\n  const [youtubeUrl, setYoutubeUrl] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState<AnalysisResult | null>(null);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setResult(null);\n\n    try {\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ youtubeUrl }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Щось пішло не так');\n      }\n\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Невідома помилка');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Аналізатор субтитрів YouTube\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-4\">\n            Вставте посилання на YouTube відео для автоматичного аналізу субтитрів за допомогою Gemini AI\n          </p>\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800\">\n            <p className=\"font-semibold mb-2\">💡 Для демонстрації функціональності:</p>\n            <p>Використайте посилання: <code className=\"bg-blue-100 px-2 py-1 rounded\">https://www.youtube.com/watch?v=demo</code></p>\n            <p className=\"mt-2 text-blue-600\">Це покаже як працює аналіз з тестовими даними.</p>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-xl p-8 mb-8\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"youtube-url\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                YouTube посилання\n              </label>\n              <input\n                type=\"url\"\n                id=\"youtube-url\"\n                value={youtubeUrl}\n                onChange={(e) => setYoutubeUrl(e.target.value)}\n                placeholder=\"https://www.youtube.com/watch?v=...\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                required\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {loading ? 'Аналізую...' : 'Аналізувати відео'}\n            </button>\n          </form>\n\n          {error && (\n            <div className=\"mt-6 p-4 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-red-800\">{error}</p>\n            </div>\n          )}\n        </div>\n\n        {result && (\n          <div className=\"bg-white rounded-lg shadow-xl p-8\">\n            <div className=\"mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Результати аналізу</h2>\n              <div className=\"flex flex-wrap gap-4 text-sm text-gray-600\">\n                <span>Video ID: {result.videoId}</span>\n                <span>Довжина тексту: {result.transcriptLength} символів</span>\n              </div>\n            </div>\n\n            <div className=\"prose max-w-none\">\n              <div className=\"whitespace-pre-wrap text-gray-800 leading-relaxed\">\n                {result.analysis}\n              </div>\n            </div>\n\n            {result.rawTranscript && result.rawTranscript.length > 0 && (\n              <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Перші фрагменти субтитрів:\n                </h3>\n                <div className=\"space-y-2\">\n                  {result.rawTranscript.map((item, index) => (\n                    <div key={index} className=\"text-sm text-gray-600\">\n                      <span className=\"font-mono text-xs text-gray-400 mr-2\">\n                        {Math.floor(item.offset / 1000)}s:\n                      </span>\n                      {item.text}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAW;YACpC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;;wCAAE;sDAAwB,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;8CAC3E,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;8BAItC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA+C;;;;;;sDAGtF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,gBAAgB;;;;;;;;;;;;wBAI9B,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;gBAKlC,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDAAW,OAAO,OAAO;;;;;;;sDAC/B,8OAAC;;gDAAK;gDAAiB,OAAO,gBAAgB;gDAAC;;;;;;;;;;;;;;;;;;;sCAInD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,QAAQ;;;;;;;;;;;wBAInB,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,MAAM,GAAG,mBACrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;8CACZ,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;wDAAM;;;;;;;gDAEjC,KAAK,IAAI;;2CAJF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe9B", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}