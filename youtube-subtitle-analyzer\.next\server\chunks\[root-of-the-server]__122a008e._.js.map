{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { YoutubeTranscript } from 'youtube-transcript';\nimport { GoogleGenerativeAI } from '@google/generative-ai';\n\n// Ініціалізація Gemini AI\nconst genAI = new GoogleGenerativeAI('AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM');\n\n// Функція для отримання YouTube video ID з URL\nfunction getYouTubeVideoId(url: string): string | null {\n  const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n  const match = url.match(regex);\n  return match ? match[1] : null;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { youtubeUrl } = await request.json();\n\n    if (!youtubeUrl) {\n      return NextResponse.json(\n        { error: 'YouTube URL є обов\\'язковим' },\n        { status: 400 }\n      );\n    }\n\n    // Отримання video ID\n    const videoId = getYouTubeVideoId(youtubeUrl);\n    if (!videoId) {\n      return NextResponse.json(\n        { error: 'Невірний YouTube URL' },\n        { status: 400 }\n      );\n    }\n\n    // Отримання субтитрів\n    let transcript;\n    try {\n      transcript = await YoutubeTranscript.fetchTranscript(videoId);\n    } catch (error) {\n      return NextResponse.json(\n        { error: 'Не вдалося отримати субтитри. Можливо, вони недоступні для цього відео.' },\n        { status: 404 }\n      );\n    }\n\n    // Об'єднання тексту субтитрів\n    const fullText = transcript.map(item => item.text).join(' ');\n\n    if (!fullText.trim()) {\n      return NextResponse.json(\n        { error: 'Субтитри порожні або недоступні' },\n        { status: 404 }\n      );\n    }\n\n    // Аналіз через Gemini AI\n    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });\n\n    const prompt = `\nПроаналізуй наступні субтитри з YouTube відео та надай детальний аналіз:\n\nСУБТИТРИ:\n${fullText}\n\nНадай аналіз у наступному форматі:\n\n1. ОСНОВНА ТЕМА:\n[Коротко опиши основну тему відео]\n\n2. КЛЮЧОВІ МОМЕНТИ:\n[Перелічи 5-7 найважливіших моментів або ідей]\n\n3. СТРУКТУРА КОНТЕНТУ:\n[Опиши як організований контент - чи є вступ, основна частина, висновки]\n\n4. ТОНАЛЬНІСТЬ:\n[Опиши загальну тональність - формальна, неформальна, освітня, розважальна тощо]\n\n5. ЦІЛЬОВА АУДИТОРІЯ:\n[Визнач для якої аудиторії призначений контент]\n\n6. ВИСНОВКИ ТА РЕКОМЕНДАЦІЇ:\n[Твої висновки про якість контенту та рекомендації]\n\n7. КЛЮЧОВІ СЛОВА:\n[5-10 найважливіших ключових слів або фраз]\n\nВідповідай українською мовою.\n`;\n\n    const result = await model.generateContent(prompt);\n    const analysis = result.response.text();\n\n    return NextResponse.json({\n      success: true,\n      videoId,\n      transcriptLength: fullText.length,\n      analysis,\n      rawTranscript: transcript.slice(0, 10) // Перші 10 елементів для перегляду\n    });\n\n  } catch (error) {\n    console.error('Помилка при аналізі:', error);\n    return NextResponse.json(\n      { error: 'Внутрішня помилка сервера' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,0BAA0B;AAC1B,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;AAErC,+CAA+C;AAC/C,SAAS,kBAAkB,GAAW;IACpC,MAAM,QAAQ;IACd,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,UAAU,kBAAkB;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI;QACJ,IAAI;YACF,aAAa,MAAM,+KAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0E,GACnF;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,WAAW,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;QAExD,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAmB;QAEnE,MAAM,SAAS,CAAC;;;;AAIpB,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BX,CAAC;QAEG,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,kBAAkB,SAAS,MAAM;YACjC;YACA,eAAe,WAAW,KAAK,CAAC,GAAG,IAAI,mCAAmC;QAC5E;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}