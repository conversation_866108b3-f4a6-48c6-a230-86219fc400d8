{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/%D0%A0%D0%BE%D0%B1%D0%BE%D1%87%D0%B8%D0%B9%20%D1%81%D1%82%D1%96%D0%BB/ai/youtube-subtitle-analyzer/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { YoutubeTranscript } from 'youtube-transcript';\nimport { GoogleGenerativeAI } from '@google/generative-ai';\n\n// Ініціалізація Gemini AI\nconst genAI = new GoogleGenerativeAI('AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM');\n\n// Функція для отримання YouTube video ID з URL\nfunction getYouTubeVideoId(url: string): string | null {\n  const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n  const match = url.match(regex);\n  return match ? match[1] : null;\n}\n\nexport async function GET() {\n  return NextResponse.json({ message: 'API працює' });\n}\n\nexport async function POST(request: NextRequest) {\n  console.log('POST запит отримано');\n  try {\n    const body = await request.json();\n    console.log('Тіло запиту:', body);\n    const { youtubeUrl } = body;\n\n    if (!youtubeUrl) {\n      console.log('YouTube URL відсутній');\n      return NextResponse.json(\n        { error: 'YouTube URL є обов\\'язковим' },\n        { status: 400 }\n      );\n    }\n\n    console.log('YouTube URL:', youtubeUrl);\n\n    // Отримання video ID або перевірка на демо режим\n    let videoId = getYouTubeVideoId(youtubeUrl);\n\n    // Перевірка на демо режим\n    if (youtubeUrl.includes('demo')) {\n      videoId = 'demo';\n    }\n\n    console.log('Video ID:', videoId);\n    if (!videoId) {\n      console.log('Невірний YouTube URL');\n      return NextResponse.json(\n        { error: 'Невірний YouTube URL. Спробуйте використати \"https://www.youtube.com/watch?v=demo\" для демонстрації.' },\n        { status: 400 }\n      );\n    }\n\n    // Отримання субтитрів\n    console.log('Спроба отримати субтитри для video ID:', videoId);\n    let transcript;\n    let fullText;\n\n    // Тестовий режим для демонстрації\n    if (videoId === 'demo') {\n      console.log('Використовується демо режим');\n      fullText = `Це демонстраційний текст для показу роботи аналізатора субтитрів.\n      У цьому відео розповідається про важливість штучного інтелекту в сучасному світі.\n      Спікер пояснює основні концепції машинного навчання та їх застосування в різних галузях.\n      Відео має освітній характер і призначене для широкої аудиторії.\n      Основні теми включають: нейронні мережі, обробка природної мови, комп'ютерний зір,\n      етичні аспекти ШІ та майбутні перспективи розвитку технологій.`;\n      transcript = [\n        { text: 'Це демонстраційний', offset: 0, duration: 1000 },\n        { text: 'текст для показу', offset: 1000, duration: 1000 },\n        { text: 'роботи аналізатора', offset: 2000, duration: 1000 }\n      ];\n    } else {\n      try {\n        transcript = await YoutubeTranscript.fetchTranscript(videoId);\n        console.log('Субтитри отримано, кількість елементів:', transcript.length);\n\n        if (!transcript || transcript.length === 0) {\n          console.log('Субтитри порожні, використовуємо демо режим');\n          return NextResponse.json(\n            {\n              error: 'Субтитри недоступні для цього відео. Спробуйте використати URL \"https://www.youtube.com/watch?v=demo\" для демонстрації функціональності.'\n            },\n            { status: 404 }\n          );\n        }\n\n        fullText = transcript.map(item => item.text).join(' ');\n      } catch (error) {\n        console.log('Помилка отримання субтитрів:', error);\n        return NextResponse.json(\n          {\n            error: 'Не вдалося отримати субтитри. Спробуйте використати URL \"https://www.youtube.com/watch?v=demo\" для демонстрації функціональності.'\n          },\n          { status: 404 }\n        );\n      }\n    }\n\n    console.log('Повний текст субтитрів (перші 100 символів):', fullText.substring(0, 100));\n\n    if (!fullText.trim()) {\n      console.log('Субтитри порожні');\n      return NextResponse.json(\n        { error: 'Субтитри порожні. Спробуйте використати URL \"https://www.youtube.com/watch?v=demo\" для демонстрації.' },\n        { status: 404 }\n      );\n    }\n\n    // Аналіз через Gemini AI\n    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });\n\n    const prompt = `\nПроаналізуй наступні субтитри з YouTube відео та надай детальний аналіз:\n\nСУБТИТРИ:\n${fullText}\n\nНадай аналіз у наступному форматі:\n\n1. ОСНОВНА ТЕМА:\n[Коротко опиши основну тему відео]\n\n2. КЛЮЧОВІ МОМЕНТИ:\n[Перелічи 5-7 найважливіших моментів або ідей]\n\n3. СТРУКТУРА КОНТЕНТУ:\n[Опиши як організований контент - чи є вступ, основна частина, висновки]\n\n4. ТОНАЛЬНІСТЬ:\n[Опиши загальну тональність - формальна, неформальна, освітня, розважальна тощо]\n\n5. ЦІЛЬОВА АУДИТОРІЯ:\n[Визнач для якої аудиторії призначений контент]\n\n6. ВИСНОВКИ ТА РЕКОМЕНДАЦІЇ:\n[Твої висновки про якість контенту та рекомендації]\n\n7. КЛЮЧОВІ СЛОВА:\n[5-10 найважливіших ключових слів або фраз]\n\nВідповідай українською мовою.\n`;\n\n    const result = await model.generateContent(prompt);\n    const analysis = result.response.text();\n\n    return NextResponse.json({\n      success: true,\n      videoId,\n      transcriptLength: fullText.length,\n      analysis,\n      rawTranscript: transcript.slice(0, 10) // Перші 10 елементів для перегляду\n    });\n\n  } catch (error) {\n    console.error('Помилка при аналізі:', error);\n    return NextResponse.json(\n      { error: 'Внутрішня помилка сервера' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,0BAA0B;AAC1B,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC;AAErC,+CAA+C;AAC/C,SAAS,kBAAkB,GAAW;IACpC,MAAM,QAAQ;IACd,MAAM,QAAQ,IAAI,KAAK,CAAC;IACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAa;AACnD;AAEO,eAAe,KAAK,OAAoB;IAC7C,QAAQ,GAAG,CAAC;IACZ,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,IAAI,CAAC,YAAY;YACf,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,iDAAiD;QACjD,IAAI,UAAU,kBAAkB;QAEhC,0BAA0B;QAC1B,IAAI,WAAW,QAAQ,CAAC,SAAS;YAC/B,UAAU;QACZ;QAEA,QAAQ,GAAG,CAAC,aAAa;QACzB,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuG,GAChH;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,QAAQ,GAAG,CAAC,0CAA0C;QACtD,IAAI;QACJ,IAAI;QAEJ,kCAAkC;QAClC,IAAI,YAAY,QAAQ;YACtB,QAAQ,GAAG,CAAC;YACZ,WAAW,CAAC;;;;;oEAKkD,CAAC;YAC/D,aAAa;gBACX;oBAAE,MAAM;oBAAsB,QAAQ;oBAAG,UAAU;gBAAK;gBACxD;oBAAE,MAAM;oBAAoB,QAAQ;oBAAM,UAAU;gBAAK;gBACzD;oBAAE,MAAM;oBAAsB,QAAQ;oBAAM,UAAU;gBAAK;aAC5D;QACH,OAAO;YACL,IAAI;gBACF,aAAa,MAAM,+KAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC;gBACrD,QAAQ,GAAG,CAAC,2CAA2C,WAAW,MAAM;gBAExE,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;oBAC1C,QAAQ,GAAG,CAAC;oBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;oBACT,GACA;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,WAAW,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;YACpD,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,gCAAgC;gBAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO;gBACT,GACA;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,QAAQ,GAAG,CAAC,gDAAgD,SAAS,SAAS,CAAC,GAAG;QAElF,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuG,GAChH;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAmB;QAEnE,MAAM,SAAS,CAAC;;;;AAIpB,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BX,CAAC;QAEG,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,kBAAkB,SAAS,MAAM;YACjC;YACA,eAAe,WAAW,KAAK,CAAC,GAAG,IAAI,mCAAmC;QAC5E;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}