import { NextRequest, NextResponse } from 'next/server';
import { YoutubeTranscript } from 'youtube-transcript';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Ініціалізація Gemini AI
const genAI = new GoogleGenerativeAI('AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM');

// Функція для отримання YouTube video ID з URL
function getYouTubeVideoId(url: string): string | null {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

export async function POST(request: NextRequest) {
  try {
    const { youtubeUrl } = await request.json();

    if (!youtubeUrl) {
      return NextResponse.json(
        { error: 'YouTube URL є обов\'язковим' },
        { status: 400 }
      );
    }

    // Отримання video ID
    const videoId = getYouTubeVideoId(youtubeUrl);
    if (!videoId) {
      return NextResponse.json(
        { error: 'Невірний YouTube URL' },
        { status: 400 }
      );
    }

    // Отримання субтитрів
    let transcript;
    try {
      transcript = await YoutubeTranscript.fetchTranscript(videoId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Не вдалося отримати субтитри. Можливо, вони недоступні для цього відео.' },
        { status: 404 }
      );
    }

    // Об'єднання тексту субтитрів
    const fullText = transcript.map(item => item.text).join(' ');

    if (!fullText.trim()) {
      return NextResponse.json(
        { error: 'Субтитри порожні або недоступні' },
        { status: 404 }
      );
    }

    // Аналіз через Gemini AI
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Проаналізуй наступні субтитри з YouTube відео та надай детальний аналіз:

СУБТИТРИ:
${fullText}

Надай аналіз у наступному форматі:

1. ОСНОВНА ТЕМА:
[Коротко опиши основну тему відео]

2. КЛЮЧОВІ МОМЕНТИ:
[Перелічи 5-7 найважливіших моментів або ідей]

3. СТРУКТУРА КОНТЕНТУ:
[Опиши як організований контент - чи є вступ, основна частина, висновки]

4. ТОНАЛЬНІСТЬ:
[Опиши загальну тональність - формальна, неформальна, освітня, розважальна тощо]

5. ЦІЛЬОВА АУДИТОРІЯ:
[Визнач для якої аудиторії призначений контент]

6. ВИСНОВКИ ТА РЕКОМЕНДАЦІЇ:
[Твої висновки про якість контенту та рекомендації]

7. КЛЮЧОВІ СЛОВА:
[5-10 найважливіших ключових слів або фраз]

Відповідай українською мовою.
`;

    const result = await model.generateContent(prompt);
    const analysis = result.response.text();

    return NextResponse.json({
      success: true,
      videoId,
      transcriptLength: fullText.length,
      analysis,
      rawTranscript: transcript.slice(0, 10) // Перші 10 елементів для перегляду
    });

  } catch (error) {
    console.error('Помилка при аналізі:', error);
    return NextResponse.json(
      { error: 'Внутрішня помилка сервера' },
      { status: 500 }
    );
  }
}
