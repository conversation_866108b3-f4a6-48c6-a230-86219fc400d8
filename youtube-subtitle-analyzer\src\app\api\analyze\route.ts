import { NextRequest, NextResponse } from 'next/server';
import { YoutubeTranscript } from 'youtube-transcript';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Ініціалізація Gemini AI
const genAI = new GoogleGenerativeAI('AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM');

// Функція для отримання YouTube video ID з URL
function getYouTubeVideoId(url: string): string | null {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

export async function GET() {
  return NextResponse.json({ message: 'API працює' });
}

export async function POST(request: NextRequest) {
  console.log('POST запит отримано');
  try {
    const body = await request.json();
    console.log('Тіло запиту:', body);
    const { youtubeUrl } = body;

    if (!youtubeUrl) {
      console.log('YouTube URL відсутній');
      return NextResponse.json(
        { error: 'YouTube URL є обов\'язковим' },
        { status: 400 }
      );
    }

    console.log('YouTube URL:', youtubeUrl);

    // Отримання video ID або перевірка на демо режим
    let videoId = getYouTubeVideoId(youtubeUrl);

    // Перевірка на демо режим
    if (youtubeUrl.includes('demo')) {
      videoId = 'demo';
    }

    console.log('Video ID:', videoId);
    if (!videoId) {
      console.log('Невірний YouTube URL');
      return NextResponse.json(
        { error: 'Невірний YouTube URL. Спробуйте використати "https://www.youtube.com/watch?v=demo" для демонстрації.' },
        { status: 400 }
      );
    }

    // Отримання субтитрів
    console.log('Спроба отримати субтитри для video ID:', videoId);
    let transcript;
    let fullText;

    // Тестовий режим для демонстрації
    if (videoId === 'demo') {
      console.log('Використовується демо режим');
      fullText = `Це демонстраційний текст для показу роботи аналізатора субтитрів.
      У цьому відео розповідається про важливість штучного інтелекту в сучасному світі.
      Спікер пояснює основні концепції машинного навчання та їх застосування в різних галузях.
      Відео має освітній характер і призначене для широкої аудиторії.
      Основні теми включають: нейронні мережі, обробка природної мови, комп'ютерний зір,
      етичні аспекти ШІ та майбутні перспективи розвитку технологій.`;
      transcript = [
        { text: 'Це демонстраційний', offset: 0, duration: 1000 },
        { text: 'текст для показу', offset: 1000, duration: 1000 },
        { text: 'роботи аналізатора', offset: 2000, duration: 1000 }
      ];
    } else {
      try {
        transcript = await YoutubeTranscript.fetchTranscript(videoId);
        console.log('Субтитри отримано, кількість елементів:', transcript.length);

        if (!transcript || transcript.length === 0) {
          console.log('Субтитри порожні, використовуємо демо режим');
          return NextResponse.json(
            {
              error: 'Субтитри недоступні для цього відео. Спробуйте використати URL "https://www.youtube.com/watch?v=demo" для демонстрації функціональності.'
            },
            { status: 404 }
          );
        }

        fullText = transcript.map(item => item.text).join(' ');
      } catch (error) {
        console.log('Помилка отримання субтитрів:', error);
        return NextResponse.json(
          {
            error: 'Не вдалося отримати субтитри. Спробуйте використати URL "https://www.youtube.com/watch?v=demo" для демонстрації функціональності.'
          },
          { status: 404 }
        );
      }
    }

    console.log('Повний текст субтитрів (перші 100 символів):', fullText.substring(0, 100));

    if (!fullText.trim()) {
      console.log('Субтитри порожні');
      return NextResponse.json(
        { error: 'Субтитри порожні. Спробуйте використати URL "https://www.youtube.com/watch?v=demo" для демонстрації.' },
        { status: 404 }
      );
    }

    // Аналіз через Gemini AI
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Проаналізуй наступні субтитри з YouTube відео та надай детальний аналіз:

СУБТИТРИ:
${fullText}

Надай аналіз у наступному форматі:

1. ОСНОВНА ТЕМА:
[Коротко опиши основну тему відео]

2. КЛЮЧОВІ МОМЕНТИ:
[Перелічи 5-7 найважливіших моментів або ідей]

3. СТРУКТУРА КОНТЕНТУ:
[Опиши як організований контент - чи є вступ, основна частина, висновки]

4. ТОНАЛЬНІСТЬ:
[Опиши загальну тональність - формальна, неформальна, освітня, розважальна тощо]

5. ЦІЛЬОВА АУДИТОРІЯ:
[Визнач для якої аудиторії призначений контент]

6. ВИСНОВКИ ТА РЕКОМЕНДАЦІЇ:
[Твої висновки про якість контенту та рекомендації]

7. КЛЮЧОВІ СЛОВА:
[5-10 найважливіших ключових слів або фраз]

Відповідай українською мовою.
`;

    const result = await model.generateContent(prompt);
    const analysis = result.response.text();

    return NextResponse.json({
      success: true,
      videoId,
      transcriptLength: fullText.length,
      analysis,
      rawTranscript: transcript.slice(0, 10) // Перші 10 елементів для перегляду
    });

  } catch (error) {
    console.error('Помилка при аналізі:', error);
    return NextResponse.json(
      { error: 'Внутрішня помилка сервера' },
      { status: 500 }
    );
  }
}
