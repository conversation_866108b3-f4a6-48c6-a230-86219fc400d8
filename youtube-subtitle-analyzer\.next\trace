[{"name": "hot-reloader", "duration": 74, "timestamp": 3135257703756, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750440726435, "traceId": "5160112353e9948f"}, {"name": "setup-dev-bundler", "duration": 762302, "timestamp": 3135257428711, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750440726160, "traceId": "5160112353e9948f"}, {"name": "run-instrumentation-hook", "duration": 28, "timestamp": 3135258279702, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750440727011, "traceId": "5160112353e9948f"}, {"name": "start-dev-server", "duration": 1582350, "timestamp": 3135256735319, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "3293564928", "memory.totalMem": "17092993024", "memory.heapSizeLimit": "8596226048", "memory.rss": "189997056", "memory.heapTotal": "101593088", "memory.heapUsed": "70427384"}, "startTime": 1750440725467, "traceId": "5160112353e9948f"}, {"name": "compile-path", "duration": 806309, "timestamp": 3135283282769, "id": 7, "tags": {"trigger": "/api/analyze"}, "startTime": 1750440752014, "traceId": "5160112353e9948f"}, {"name": "ensure-page", "duration": 807658, "timestamp": 3135283282008, "id": 6, "parentId": 3, "tags": {"inputPage": "/api/analyze/route"}, "startTime": 1750440752013, "traceId": "5160112353e9948f"}]