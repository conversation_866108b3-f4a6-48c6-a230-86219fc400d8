'use client';

import { useState } from 'react';

interface AnalysisResult {
  success: boolean;
  videoId: string;
  transcriptLength: number;
  analysis: string;
  rawTranscript: Array<{ text: string; offset: number; duration: number }>;
}

export default function Home() {
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ youtubeUrl }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Щось пішло не так');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Невідома помилка');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Аналізатор субтитрів YouTube
          </h1>
          <p className="text-xl text-gray-600 mb-4">
            Вставте посилання на YouTube відео для автоматичного аналізу субтитрів за допомогою Gemini AI
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
            <p className="font-semibold mb-2">💡 Для демонстрації функціональності:</p>
            <p>Використайте посилання: <code className="bg-blue-100 px-2 py-1 rounded">https://www.youtube.com/watch?v=demo</code></p>
            <p className="mt-2 text-blue-600">Це покаже як працює аналіз з тестовими даними.</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-xl p-8 mb-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="youtube-url" className="block text-sm font-medium text-gray-700 mb-2">
                YouTube посилання
              </label>
              <input
                type="url"
                id="youtube-url"
                value={youtubeUrl}
                onChange={(e) => setYoutubeUrl(e.target.value)}
                placeholder="https://www.youtube.com/watch?v=..."
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                required
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Аналізую...' : 'Аналізувати відео'}
            </button>
          </form>

          {error && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{error}</p>
            </div>
          )}
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow-xl p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Результати аналізу</h2>
              <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                <span>Video ID: {result.videoId}</span>
                <span>Довжина тексту: {result.transcriptLength} символів</span>
              </div>
            </div>

            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                {result.analysis}
              </div>
            </div>

            {result.rawTranscript && result.rawTranscript.length > 0 && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Перші фрагменти субтитрів:
                </h3>
                <div className="space-y-2">
                  {result.rawTranscript.map((item, index) => (
                    <div key={index} className="text-sm text-gray-600">
                      <span className="font-mono text-xs text-gray-400 mr-2">
                        {Math.floor(item.offset / 1000)}s:
                      </span>
                      {item.text}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
